import React, { useState } from "react"
import { Button, Input, Typography, createTheme, ThemeProvider } from "@apollo/ui"
import { Meta } from "@storybook/addon-docs/blocks"
import { <PERSON><PERSON>, <PERSON> } from "@storybook/addon-docs"

<Meta title="Quick Start" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >Quick Start</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Install the library with ease and start building amazing UIs!</Typography>
</div>

## Installation

Currently our Apollo Design System is still being a private design system used internally at our company.

To install the `@apollo/ui` package, we need to configure the `.npmrc` file (create this file in the project root) to point to our custom private registry on GitLab.

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
```

### Install the Package

:::caution
If you're outside CJ network, VPN is needed to install this package.
:::

```bash
# with pnpm (recommended)
pnpm add @apollo/ui

# with yarn
yarn add @apollo/ui

# with npm
npm install @apollo/ui
```

### Set up the App

To ensure consistent styling, use the `ThemeProvider` and import `style.css` from the `@apollo/ui` package. It injects all design token CSS variables into the DOM.

```jsx title="App.js"
import { createTheme, ThemeProvider } from "@apollo/ui"
import "@apollo/ui/style.css"

const appTheme = createTheme()

function App({ children }) {
  return <ThemeProvider theme={appTheme}>{children}</ThemeProvider>
}
```

## Basic Usage

You can import and use components directly:

```jsx title="Button Example"
import { Button } from "@apollo/ui"

export default function Example() {
  return <Button>Hello World</Button>
}
```

<Canvas>
  <Story name="Basic Button">
    {() => <Button>Hello World</Button>}
  </Story>
</Canvas>

## Interactive Example

Here's a more comprehensive example showing multiple components working together:

<Canvas>
  <Story name="Interactive Form">
    {() => {
      const [name, setName] = useState("")
      const [email, setEmail] = useState("")
      
      const handleSubmit = () => {
        alert(`Hello ${name}! Email: ${email}`)
      }
      
      return (
        <div style={{ display: "flex", flexDirection: "column", gap: 16, maxWidth: 400 }}>
          <Typography level="headlineMedium">Contact Form</Typography>
          <Input
            label="Name"
            placeholder="Enter your name"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
          <Input
            label="Email"
            placeholder="Enter your email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <Button onClick={handleSubmit} disabled={!name || !email}>
            Submit
          </Button>
        </div>
      )
    }}
  </Story>
</Canvas>

## Next Steps

Now that you have Apollo UI set up, explore our component library:

- **[Components](?path=/docs/@apollo∕ui--docs)** - Browse all available components
- **[Foundations](?path=/docs/foundations--docs)** - Learn about design tokens and foundations
- **[Theming](?path=/docs/@apollo∕ui-theming--docs)** - Customize themes for your application

### Key Features

- 🎨 **Design Tokens** - Consistent design language with CSS custom properties
- 🌙 **Dark Mode** - Built-in support for light and dark themes
- ♿ **Accessibility** - WCAG compliant components
- 📱 **Responsive** - Mobile-first responsive design
- 🔧 **TypeScript** - Full TypeScript support with type definitions

### Need Help?

- Check out our [Migration Guide](?path=/docs/migration-guide--docs) if you're upgrading from legacy components
- Browse [component examples](?path=/docs/@apollo∕ui--docs) for detailed usage patterns
- Review [design foundations](?path=/docs/foundations--docs) for design principles

Happy building! 🚀
